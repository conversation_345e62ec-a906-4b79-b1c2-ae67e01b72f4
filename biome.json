{"$schema": "https://biomejs.dev/schemas/2.1.3/schema.json", "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"recommended": true}, "complexity": {"recommended": true, "noExcessiveCognitiveComplexity": "warn", "noVoid": "off"}, "correctness": {"recommended": true, "noUnusedVariables": "error", "noUnusedImports": "error"}, "nursery": {"useSortedClasses": "error"}, "performance": {"recommended": true}, "security": {"recommended": true}, "style": {"recommended": true, "noNonNullAssertion": "warn", "useImportType": "error", "useNodejsImportProtocol": "error", "useNumberNamespace": "error"}, "suspicious": {"recommended": true, "noExplicitAny": "warn", "noArrayIndexKey": "warn"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto"}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto"}}, "json": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100}}, "files": {"includes": ["packages/**/*.{js,jsx,ts,tsx,json,jsonc}", "*.{js,jsx,ts,tsx,json,jsonc}", "**/*.{js,jsx,ts,tsx,json,jsonc}"], "ignoreUnknown": false}, "overrides": [{"includes": ["**/*.test.{js,jsx,ts,tsx}", "**/*.spec.{js,jsx,ts,tsx}", "**/tests/**"], "linter": {"rules": {"suspicious": {"noExplicitAny": "off"}}}}, {"includes": ["**/*.config.{js,ts,mjs}", "**/*.config.*.{js,ts,mjs}"], "linter": {"rules": {"style": {"useNodejsImportProtocol": "off"}}}}, {"includes": ["packages/**/utils.ts", "packages/**/lib/utils.ts", "packages/**/shared/lib/utils.ts", "packages/**/rule-sections.ts", "packages/**/locale.ts"], "linter": {"rules": {"complexity": {"noExcessiveCognitiveComplexity": "off"}}}}, {"includes": ["packages/web/hooks/use-rule-queries.ts"], "linter": {"rules": {"suspicious": {"noExplicitAny": "off"}}}}, {"includes": ["packages/web/lib/auth.ts"], "linter": {"rules": {"suspicious": {"noExplicitAny": "off"}}}}, {"includes": ["packages/web/components/layout/footer.tsx"], "linter": {"rules": {"security": {"noDangerouslySetInnerHtml": "off"}}}}]}