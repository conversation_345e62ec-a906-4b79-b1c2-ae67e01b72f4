---
title: Quick Start Guide
description: Get started with OnlyRules in just a few minutes
---

# Quick Start Guide

Welcome to OnlyRules! This guide will help you get started with creating and using AI prompt rules for your favorite IDEs.

## What is OnlyRules?

OnlyRules is an AI Prompt Management Platform that helps developers:
- Create custom AI prompt rules for different IDEs
- Share and discover community-created rules
- Organize rules with tags and categories
- Export rules in various formats

## Step 1: Create an Account

1. Visit [OnlyRules](/) 
2. Click **Sign Up** in the navigation
3. Fill in your details and create your account
4. Verify your email address

## Step 2: Browse Existing Rules

Before creating your own rules, explore what's already available:

1. Go to the [Dashboard](/dashboard)
2. Browse rules by IDE type or tags
3. Click on any rule to view its content
4. Use the search functionality to find specific rules

## Step 3: Create Your First Rule

1. Click **Create Rule** from the dashboard
2. Fill in the rule details:
   - **Title**: Give your rule a descriptive name
   - **Description**: Explain what your rule does
   - **IDE Type**: Select your target IDE (Cursor, Windsurf, etc.)
   - **Tags**: Add relevant tags for discoverability
   - **Content**: Write your prompt rule content

3. Click **Save** to create your rule

### Example Rule Content

Here's a simple example for a React component rule:

```markdown
# React Component Generator

You are an expert React developer. When I ask you to create a component:

1. Use TypeScript with proper type definitions
2. Follow React best practices and hooks
3. Include proper error handling
4. Add JSDoc comments for props
5. Use modern React patterns (functional components, hooks)
6. Ensure accessibility with proper ARIA attributes

Example structure:
```tsx
interface ComponentProps {
  // Define props here
}

/**
 * Component description
 */
export function ComponentName({ prop1, prop2 }: ComponentProps) {
  // Component logic
  return (
    <div>
      {/* JSX content */}
    </div>
  );
}
```

Always ask for clarification if requirements are unclear.
```

## Step 4: Use Rules in Your IDE

### For Cursor IDE

1. Copy the rule content
2. Open Cursor settings
3. Go to "Rules" or "Prompts" section
4. Paste your rule content
5. Save and start using it

### For Other IDEs

Each IDE has different ways to import rules. Check our [IDE-specific guides](/docs/ides) for detailed instructions.

## Step 5: Share and Collaborate

1. Make your rules public to share with the community
2. Add descriptive tags to help others find your rules
3. Browse and fork rules from other developers
4. Contribute improvements to existing rules

## Next Steps

- **Explore Templates**: Check out our [template library](/templates) for pre-built rule collections
- **Learn the API**: Use our [API](/docs/api) to integrate OnlyRules into your workflow
- **Join the Community**: Connect with other developers using OnlyRules
- **IDE Integration**: Set up deeper integration with your favorite IDE

## Tips for Writing Great Rules

1. **Be Specific**: Clear, specific instructions work better than vague ones
2. **Include Examples**: Show the AI what you want with concrete examples
3. **Set Context**: Explain the role the AI should take (expert developer, code reviewer, etc.)
4. **Define Constraints**: Specify what to avoid or requirements to follow
5. **Test Thoroughly**: Try your rules with different scenarios

## Getting Help

- Check our [documentation](/docs) for detailed guides
- Visit our [GitHub repository](https://github.com/ranglang/onlyrules) for issues and contributions
- Join our community discussions

Ready to boost your coding productivity with AI? Start creating your first rule today!
