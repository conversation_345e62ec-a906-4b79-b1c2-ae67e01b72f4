// This file is auto-generated by Fumadocs
export const map = [
  {
    file: 'content/docs/index.mdx',
    path: ['index'],
    data: {
      title: 'Welcome to OnlyRules Documentation',
      description: 'Learn how to use OnlyRules AI Prompt Management Platform',
    },
  },
  {
    file: 'content/docs/getting-started/quick-start.mdx',
    path: ['getting-started', 'quick-start'],
    data: {
      title: 'Quick Start Guide',
      description: 'Get started with OnlyRules in just a few minutes',
    },
  },
  {
    file: 'content/docs/ides/cursor.mdx',
    path: ['ides', 'cursor'],
    data: {
      title: 'Cursor IDE Integration',
      description: 'Learn how to use OnlyRules with Cursor IDE',
    },
  },
  {
    file: 'content/docs/api/overview.mdx',
    path: ['api', 'overview'],
    data: {
      title: 'API Overview',
      description: 'Learn about the OnlyRules API endpoints and how to use them',
    },
  },
  {
    file: 'content/docs/guides/seo-setup.mdx',
    path: ['guides', 'seo-setup'],
    data: {
      title: 'SEO Setup for OnlyRules',
      description: 'Learn how to configure SEO enhancements for the OnlyRules Next.js application',
    },
  },
  {
    file: 'content/docs/guides/i18n.mdx',
    path: ['guides', 'i18n'],
    data: {
      title: 'Internationalization (i18n) Guide',
      description: 'Learn how to use Lingui.js for internationalization in OnlyRules',
    },
  },
  {
    file: 'content/docs/guides/radix-ui-theme.mdx',
    path: ['guides', 'radix-ui-theme'],
    data: {
      title: 'Radix UI Theme v3 Guide',
      description: 'Learn how to use Radix UI Theme v3 in the OnlyRules project',
    },
  },
];
