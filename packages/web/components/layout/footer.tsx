import { Box, Container, Flex, Heading, Separator, Text } from '@radix-ui/themes';
import { Code, Github, Mail, MapPin } from 'lucide-react';
import Link from 'next/link';
import { FooterLink } from './footer-link';
import { FooterSection } from './footer-section';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <Box asChild>
      <footer className="mt-auto border-[var(--gray-6)] border-t bg-[var(--gray-2)]">
        <Container size="4" className="py-12">
          {/* Main Footer Content */}
          <Flex
            direction={{ initial: 'column', md: 'row' }}
            gap={{ initial: '8', md: '12' }}
            className="mb-8"
          >
            {/* Company Information */}
            <Box className="max-w-sm flex-1">
              <Link href="/" className="mb-4 inline-block">
                <Flex align="center" gap="2">
                  <Code size={24} className="text-[var(--accent-9)]" />
                  <Heading size="5" weight="bold" className="text-[var(--gray-12)]">
                    OnlyRules
                  </Heading>
                </Flex>
              </Link>

              <Text size="2" color="gray" className="mb-4 leading-relaxed">
                The ultimate AI prompt management platform for developers. Create, organize, and
                share AI prompt rules for your favorite IDEs to boost coding productivity.
              </Text>

              {/* Contact Information */}
              <Flex direction="column" gap="2" className="mb-4">
                <Flex align="center" gap="2">
                  <Mail size={14} className="text-[var(--gray-10)]" />
                  <FooterLink href="mailto:<EMAIL>">
                    <EMAIL>
                  </FooterLink>
                </Flex>
                <Flex align="center" gap="2">
                  <MapPin size={14} className="text-[var(--gray-10)]" />
                  <Text size="2" color="gray">
                    Global Remote Team
                  </Text>
                </Flex>
              </Flex>

              {/* Social Links */}
              <Flex align="center" gap="4">
                <FooterLink
                  href="https://github.com/ranglang/onlyrules"
                  external
                  className="flex items-center gap-2 rounded-md p-2 transition-colors hover:bg-[var(--gray-3)]"
                >
                  <Github size={18} />
                  <Text size="2">GitHub</Text>
                </FooterLink>
              </Flex>
            </Box>

            {/* Navigation Sections */}
            <Flex
              direction={{ initial: 'column', sm: 'row' }}
              gap={{ initial: '8', sm: '12' }}
              className="flex-2"
            >
              {/* Product Section */}
              <FooterSection title="Product">
                <FooterLink href="/templates">Ruleset Library</FooterLink>

                <FooterLink href="/dashboard">User Dashboard</FooterLink>
                <FooterLink href="/ides">IDE Integrations</FooterLink>
                <FooterLink href="/rulesets">Rule Collections</FooterLink>
              </FooterSection>

              {/* Resources Section */}
              <FooterSection title="Resources">
                <FooterLink href="/docs">Documentation</FooterLink>
                <FooterLink href="/docs/api">API Reference</FooterLink>
                <FooterLink href="/docs/guides">Setup Guides</FooterLink>
                <FooterLink href="/docs/ides">IDE Setup</FooterLink>
                <FooterLink href="/sitemap.xml" external>
                  Sitemap
                </FooterLink>
              </FooterSection>

              {/* Community Section */}
              <FooterSection title="Community">
                <FooterLink href="https://github.com/ranglang/onlyrules" external>
                  GitHub Repository
                </FooterLink>
                <FooterLink href="https://github.com/ranglang/onlyrules/issues" external>
                  Report Issues
                </FooterLink>
                <FooterLink href="https://github.com/ranglang/onlyrules/discussions" external>
                  Discussions
                </FooterLink>
                <FooterLink href="/contact">Contact Us</FooterLink>
              </FooterSection>

              {/* Legal Section */}
              <FooterSection title="Legal">
                <FooterLink href="/privacy">Privacy Policy</FooterLink>
                <FooterLink href="/terms">Terms of Service</FooterLink>
                <FooterLink href="/docs/guides/seo-setup">SEO Guidelines</FooterLink>
              </FooterSection>
            </Flex>
          </Flex>

          <Separator size="4" className="mb-6" />

          {/* Bottom Section */}
          <Flex
            direction={{ initial: 'column', sm: 'row' }}
            align="center"
            justify="between"
            gap="4"
          >
            <Text size="2" color="gray">
              © {currentYear} OnlyRules. All rights reserved.
            </Text>

            <Flex align="center" gap="4">
              <Text size="1" color="gray">
                Built with ❤️ for developers
              </Text>
            </Flex>
          </Flex>

          {/* Structured Data for SEO */}
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                '@context': 'https://schema.org',
                '@type': 'Organization',
                name: 'OnlyRules',
                description: 'AI Prompt Management Platform for Developers',
                url: process.env.NEXT_PUBLIC_APP_URL || 'https://onlyrules.codes',
                logo: `${process.env.NEXT_PUBLIC_APP_URL || 'https://onlyrules.codes'}/logo.png`,
                sameAs: ['https://github.com/ranglang/onlyrules'],
                contactPoint: {
                  '@type': 'ContactPoint',
                  email: '<EMAIL>',
                  contactType: 'Customer Support',
                },
              }),
            }}
          />
        </Container>
      </footer>
    </Box>
  );
}
