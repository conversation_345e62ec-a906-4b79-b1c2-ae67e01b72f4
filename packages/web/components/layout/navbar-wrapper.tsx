import { Suspense } from 'react';
import { getLocale } from '@/lib/locale';
import { ClientNavbar } from './client-navbar';
import { StaticNavbar } from './static-navbar';

// Fallback component for SSG
function NavbarFallback() {
  return <StaticNavbar />;
}

async function NavbarWithLocale() {
  const locale = await getLocale();
  return <ClientNavbar locale={locale} />;
}

export function NavbarWrapper() {
  return (
    <Suspense fallback={<NavbarFallback />}>
      <NavbarWithLocale />
    </Suspense>
  );
}
