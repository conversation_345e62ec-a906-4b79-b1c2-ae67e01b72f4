import { Text } from '@radix-ui/themes';
import { ExternalLink } from 'lucide-react';
import Link from 'next/link';

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
  external?: boolean;
  className?: string;
}

export function FooterLink({ href, children, external = false, className }: FooterLinkProps) {
  const linkProps = external
    ? {
        target: '_blank' as const,
        rel: 'noopener noreferrer' as const,
      }
    : {};

  return (
    <Link href={href} {...linkProps} className={className}>
      <Text
        size="2"
        color="gray"
        className="flex items-center gap-1 transition-colors hover:text-[var(--accent-9)]"
      >
        {children}
        {external && <ExternalLink size={12} className="opacity-60" />}
      </Text>
    </Link>
  );
}
