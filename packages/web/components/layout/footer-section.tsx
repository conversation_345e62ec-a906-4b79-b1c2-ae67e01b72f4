import { Box, Flex, Heading } from '@radix-ui/themes';

interface FooterSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

export function FooterSection({ title, children, className }: FooterSectionProps) {
  return (
    <Box className={className}>
      <Heading as="h3" size="3" weight="medium" className="mb-4 text-[var(--gray-12)]">
        {title}
      </Heading>
      <Flex direction="column" gap="3">
        {children}
      </Flex>
    </Box>
  );
}
