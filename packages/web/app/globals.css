/* @import "tailwindcss"; */

/* @import "@onlyrules/docs/dist/index.css"; */
/* @import "@onlyrules/docs/css"; */
@import "./index.css";

/* Radix UI Theme - imported after Tailwind base */

@import "@radix-ui/themes/styles.css";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

/* Custom CSS Variables for Tailwind Integration */
:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 217 91% 60%;
  --primary-foreground: 0 0% 100%;
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 217 91% 60%;
  --radius: 0.5rem;
}

/* .dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 217 91% 60%;
  --primary-foreground: 0 0% 100%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 217 91% 60%;
} */


@layer base {
  * {
    border-color: var(--gray-6);
  }

  html {
    background-color: var(--color-background, white);
  }

  body {
    background-color: var(--color-background, white) !important;
    color: var(--gray-12);
    font-family: var(--default-font-family);
  }



  /* Responsive typography scale optimized for mobile */
  h1 {
    font-size: 1.875rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.25;
  }

  @media (min-width: 768px) {
    h1 {
      font-size: 2.25rem;
    }
  }

  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.25;
  }

  @media (min-width: 768px) {
    h2 {
      font-size: 1.875rem;
    }
  }

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.25;
  }

  @media (min-width: 768px) {
    h3 {
      font-size: 1.5rem;
    }
  }

  h4 {
    font-size: 1.125rem;
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.25;
  }

  @media (min-width: 768px) {
    h4 {
      font-size: 1.25rem;
    }
  }

  h5 {
    font-size: 1rem;
    font-weight: 600;
  }

  @media (min-width: 768px) {
    h5 {
      font-size: 1.125rem;
    }
  }

  h6 {
    font-size: 0.875rem;
    font-weight: 600;
  }

  @media (min-width: 768px) {
    h6 {
      font-size: 1rem;
    }
  }

  p {
    line-height: 1.5;
    font-size: 0.875rem;
  }

  @media (min-width: 375px) {
    p {
      font-size: 1rem;
      line-height: 1.75;
    }
  }

  /* Link styles */
  a {
    transition: color 0.2s ease;
    color: var(--accent-11);
  }

  a:hover {
    color: var(--accent-12);
  }

  /* Code styles */
  code {
    background-color: var(--gray-3);
    color: var(--gray-12);
    padding: 0.2rem 0.3rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-family: var(--code-font-family);
  }

  @media (min-width: 375px) {
    code {
      font-size: 0.875rem;
    }
  }

  pre code {
    background-color: transparent;
    padding: 0;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 0.25rem;
    height: 0.25rem;
  }

  @media (min-width: 375px) {
    ::-webkit-scrollbar {
      width: 0.5rem;
      height: 0.5rem;
    }
  }

  ::-webkit-scrollbar-track {
    background-color: var(--color-background);
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--gray-a6);
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: var(--gray-a8);
  }

  /* Mobile-specific improvements */
  @media (max-width: 414px) {
    /* Improve touch targets for small screens */
    button, 
    [role="button"],
    input[type="button"],
    input[type="submit"] {
      min-height: 44px; /* iOS recommended minimum touch target */
      min-width: 44px;
    }

    /* Optimize text selection for mobile */
    * {
      -webkit-tap-highlight-color: transparent;
    }
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Mobile responsive text sizes */
  .text-mobile-responsive {
    font-size: 0.875rem;
  }

  @media (min-width: 375px) {
    .text-mobile-responsive {
      font-size: 1rem;
    }
  }

  @media (min-width: 768px) {
    .text-mobile-responsive {
      font-size: 1.125rem;
    }
  }

  .text-mobile-heading {
    font-size: 1.125rem;
  }

  @media (min-width: 375px) {
    .text-mobile-heading {
      font-size: 1.25rem;
    }
  }

  @media (min-width: 768px) {
    .text-mobile-heading {
      font-size: 1.5rem;
    }
  }

  /* Mobile touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Mobile padding utilities */
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 375px) {
    .mobile-padding {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .mobile-padding {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  .mobile-margin {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  @media (min-width: 375px) {
    .mobile-margin {
      margin-left: 1.5rem;
      margin-right: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .mobile-margin {
      margin-left: 2rem;
      margin-right: 2rem;
    }
  }

  /* Animation utilities */
  .animate-in {
    animation: animateIn 0.3s ease-out;
  }

  .animate-out {
    animation: animateOut 0.3s ease-in;
  }

  @keyframes animateIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes animateOut {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(10px);
    }
  }

  /* Focus utilities */
  .focus-visible-ring:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px var(--focus-8);
  }

  /* Mobile specific utilities */
  .mobile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 375px) {
    .mobile-container {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .mobile-container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Improved mobile grid layouts */
  .mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (min-width: 375px) {
    .mobile-grid {
      gap: 1.5rem;
    }
  }

  @media (min-width: 640px) {
    .mobile-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .mobile-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .mobile-flex {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  @media (min-width: 375px) {
    .mobile-flex {
      gap: 1rem;
    }
  }

  @media (min-width: 640px) {
    .mobile-flex {
      flex-direction: row;
    }
  }

  /* Mobile card improvements */
  .mobile-card {
    padding: 1rem;
    border-radius: 0.5rem;
  }

  @media (min-width: 375px) {
    .mobile-card {
      padding: 1.25rem;
    }
  }

  @media (min-width: 768px) {
    .mobile-card {
      padding: 1.5rem;
      border-radius: 0.75rem;
    }
  }

  /* Radix UI Theme-consistent background utilities */
  .bg-background {
    background-color: var(--color-background) !important;
  }

  .bg-panel {
    background-color: var(--color-panel-solid);
  }

  .bg-panel-translucent {
    background-color: var(--color-panel-translucent);
  }

  .bg-surface {
    background-color: var(--color-surface);
  }

  /* Fallback for compatibility with existing Tailwind classes */
  .bg-card {
    background-color: var(--color-panel-solid);
  }

  .bg-popover {
    background-color: var(--color-panel-solid);
  }

  /* Radix UI Theme-consistent text color utilities */
  .text-foreground {
    color: var(--gray-12);
  }

  .text-muted {
    color: var(--gray-11);
  }

  .text-accent {
    color: var(--accent-11);
  }

  .text-accent-contrast {
    color: var(--accent-contrast);
  }

  /* Border utilities consistent with Radix */
  .border-default {
    border-color: var(--gray-6);
  }

  .border-accent {
    border-color: var(--accent-7);
  }

}