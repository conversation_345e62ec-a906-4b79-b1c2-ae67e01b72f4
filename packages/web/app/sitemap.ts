import type { MetadataRoute } from 'next';
import prisma from '@/lib/prisma';

// Force dynamic generation
export const dynamic = 'force-dynamic';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://onlyrules.app';

  // During build, return minimal sitemap to avoid database issues
  const isBuildTime =
    process.env.NODE_ENV === 'production' &&
    !process.env.VERCEL &&
    !process.env.RAILWAY_ENVIRONMENT;
  if (isBuildTime) {
    console.log('Build environment detected, returning static sitemap only');
  }

  const idePages = [
    'cursor',
    'windsurf',
    'github-copilot',
    'claude',
    'cline',
    'augment',
    'gemini',
    'openai-codex',
    'tencent-codebuddy',
  ];

  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/templates`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/dashboard`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/auth/signin`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/auth/signup`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/ides`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
  ];

  const idePagesSitemap = idePages.map((ide) => ({
    url: `${baseUrl}/ides/${ide}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));

  // Return static pages only during build
  if (isBuildTime) {
    return [...staticPages, ...idePagesSitemap];
  }

  try {
    // Skip database operations during build if no DATABASE_URL is available
    if (!process.env.DATABASE_URL) {
      console.warn('No DATABASE_URL available, returning static sitemap only');
      return [...staticPages, ...idePagesSitemap];
    }

    // Get all public rules for sitemap
    const publicRules = await prisma.rule.findMany({
      where: { visibility: 'PUBLIC' },
      select: {
        id: true,
        updatedAt: true,
      },
      orderBy: { updatedAt: 'desc' },
      take: 1000, // Limit to prevent huge sitemaps
    });

    const rulePages = publicRules.map((rule) => ({
      url: `${baseUrl}/rules/${rule.id}`,
      lastModified: rule.updatedAt,
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }));

    // Get users with public rules for profile pages
    const usersWithPublicRules = await prisma.user.findMany({
      where: {
        rules: {
          some: {
            visibility: 'PUBLIC',
          },
        },
      },
      select: {
        id: true,
        updatedAt: true,
      },
      take: 500, // Limit to prevent huge sitemaps
    });

    const profilePages = usersWithPublicRules.map((user) => ({
      url: `${baseUrl}/profile/${user.id}`,
      lastModified: user.updatedAt,
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    }));

    return [...staticPages, ...idePagesSitemap, ...rulePages, ...profilePages];
  } catch (error) {
    console.warn('Failed to generate dynamic sitemap entries:', error);
    // Return static pages only if database is not available
    return [...staticPages, ...idePagesSitemap];
  }
}
