// Simple content components for documentation
export const DocsContent = {
  index: () => (
    <div className="prose prose-slate dark:prose-invert max-w-none">
      <h1 className="mb-6 font-bold text-4xl text-foreground">
        Welcome to OnlyRules Documentation
      </h1>
      <p className="mb-8 text-lg text-muted-foreground leading-relaxed">
        OnlyRules is an AI Prompt Management Platform that helps developers create, organize, and
        share AI prompt rules for their favorite IDEs.
      </p>

      <h2 className="mt-8 mb-4 font-semibold text-2xl text-foreground">Quick Start</h2>
      <p className="mb-4 text-muted-foreground">
        Get started with OnlyRules in just a few minutes:
      </p>
      <ol className="ml-4 list-inside list-decimal space-y-2 text-muted-foreground">
        <li>
          <strong className="text-foreground">Sign up</strong> for an account at OnlyRules
        </li>
        <li>
          <strong className="text-foreground">Browse</strong> existing templates and rules
        </li>
        <li>
          <strong className="text-foreground">Create</strong> your own custom rules
        </li>
        <li>
          <strong className="text-foreground">Share</strong> with the community
        </li>
      </ol>

      <h2 className="mt-8 mb-4 font-semibold text-2xl text-foreground">Features</h2>
      <ul className="ml-4 list-inside list-disc space-y-2">
        <li className="text-muted-foreground">
          🚀 <strong className="text-foreground">IDE Integration</strong> - Works with Cursor,
          Windsurf, GitHub Copilot, Claude, and more
        </li>
        <li className="text-muted-foreground">
          📝 <strong className="text-foreground">Rule Management</strong> - Create, edit, and
          organize your AI prompt rules
        </li>
        <li className="text-muted-foreground">
          🌐 <strong className="text-foreground">Community Sharing</strong> - Share and discover
          rules from other developers
        </li>
        <li className="text-muted-foreground">
          🎨 <strong className="text-foreground">Template System</strong> - Use pre-built templates
          to get started quickly
        </li>
        <li className="text-muted-foreground">
          🔍 <strong className="text-foreground">Search & Discovery</strong> - Find the perfect
          rules for your use case
        </li>
      </ul>

      <h2 className="mt-8 mb-4 font-semibold text-2xl text-foreground">Popular IDEs Supported</h2>
      <ul className="ml-4 list-inside list-disc space-y-2">
        <li>
          <a
            href="/docs/ides/cursor"
            className="text-primary underline-offset-4 transition-colors hover:text-primary/80 hover:underline"
          >
            Cursor
          </a>
        </li>
        <li>
          <a
            href="/docs/ides/windsurf"
            className="text-primary underline-offset-4 transition-colors hover:text-primary/80 hover:underline"
          >
            Windsurf
          </a>
        </li>
        <li>
          <a
            href="/docs/ides/github-copilot"
            className="text-primary underline-offset-4 transition-colors hover:text-primary/80 hover:underline"
          >
            GitHub Copilot
          </a>
        </li>
        <li>
          <a
            href="/docs/ides/claude"
            className="text-primary underline-offset-4 transition-colors hover:text-primary/80 hover:underline"
          >
            Claude
          </a>
        </li>
        <li>
          <a
            href="/docs/ides/cline"
            className="text-primary underline-offset-4 transition-colors hover:text-primary/80 hover:underline"
          >
            Cline
          </a>
        </li>
        <li>
          <a
            href="/docs/ides/augment"
            className="text-primary underline-offset-4 transition-colors hover:text-primary/80 hover:underline"
          >
            Augment Code
          </a>
        </li>
      </ul>
    </div>
  ),

  'getting-started/quick-start': () => (
    <div>
      <h1>Quick Start Guide</h1>
      <p>
        Welcome to OnlyRules! This guide will help you get started with creating and using AI prompt
        rules for your favorite IDEs.
      </p>

      <h2>What is OnlyRules?</h2>
      <p>OnlyRules is an AI Prompt Management Platform that helps developers:</p>
      <ul>
        <li>Create custom AI prompt rules for different IDEs</li>
        <li>Share and discover community-created rules</li>
        <li>Organize rules with tags and categories</li>
        <li>Export rules in various formats</li>
      </ul>

      <h2>Step 1: Create an Account</h2>
      <ol>
        <li>
          Visit <a href="/">OnlyRules</a>
        </li>
        <li>
          Click <strong>Sign Up</strong> in the navigation
        </li>
        <li>Fill in your details and create your account</li>
        <li>Verify your email address</li>
      </ol>

      <h2>Step 2: Browse Existing Rules</h2>
      <p>Before creating your own rules, explore what's already available:</p>
      <ol>
        <li>
          Go to the <a href="/dashboard">Dashboard</a>
        </li>
        <li>Browse rules by IDE type or tags</li>
        <li>Click on any rule to view its content</li>
        <li>Use the search functionality to find specific rules</li>
      </ol>
    </div>
  ),

  'ides/cursor': () => (
    <div>
      <h1>Cursor IDE Integration</h1>
      <p>
        Cursor is an AI-powered code editor that makes it easy to integrate custom AI prompt rules.
        This guide shows you how to use OnlyRules with Cursor.
      </p>

      <h2>What is Cursor?</h2>
      <p>
        Cursor is a fork of VS Code that's designed from the ground up to be the best way to code
        with AI. It features:
      </p>
      <ul>
        <li>Built-in AI chat and code generation</li>
        <li>Custom rule support</li>
        <li>Advanced AI features like Composer and Tab completion</li>
        <li>Full VS Code compatibility</li>
      </ul>

      <h2>Setting Up Rules in Cursor</h2>
      <h3>Method 1: Using .cursorrules File</h3>
      <ol>
        <li>
          <strong>Create a .cursorrules file</strong> in your project root
        </li>
        <li>
          <strong>Copy rule content</strong> from OnlyRules
        </li>
        <li>
          <strong>Paste into .cursorrules</strong> file
        </li>
        <li>
          <strong>Save the file</strong> - Cursor will automatically detect it
        </li>
      </ol>
    </div>
  ),

  'api/overview': () => (
    <div>
      <h1>API Overview</h1>
      <p>
        The OnlyRules API provides programmatic access to rules, templates, and user data. All API
        endpoints are RESTful and return JSON responses.
      </p>

      <h2>Base URL</h2>
      <pre>
        <code>https://onlyrules.app/api</code>
      </pre>

      <h2>Authentication</h2>
      <p>
        Most API endpoints require authentication. Include your API key in the Authorization header:
      </p>
      <pre>
        <code>{`curl -H "Authorization: Bearer YOUR_API_KEY" \\
  https://onlyrules.app/api/rules`}</code>
      </pre>

      <h2>Available Endpoints</h2>
      <h3>Rules API</h3>
      <ul>
        <li>
          <code>GET /api/rules</code> - List all rules
        </li>
        <li>
          <code>GET /api/rules/{'{id}'}</code> - Get a specific rule
        </li>
        <li>
          <code>POST /api/rules</code> - Create a new rule
        </li>
        <li>
          <code>PUT /api/rules/{'{id}'}</code> - Update a rule
        </li>
        <li>
          <code>DELETE /api/rules/{'{id}'}</code> - Delete a rule
        </li>
      </ul>
    </div>
  ),

  'guides/seo-setup': () => (
    <div>
      <h1>SEO Setup for OnlyRules</h1>
      <p>
        This document outlines the SEO enhancements implemented for the OnlyRules Next.js
        application.
      </p>

      <h2>Implemented Features</h2>
      <h3>1. Dynamic Sitemap Generation</h3>
      <ul>
        <li>
          Automatically generates a sitemap at <code>/sitemap.xml</code>
        </li>
        <li>Uses API routes for Next.js compatibility</li>
        <li>Includes all static routes with appropriate priorities</li>
      </ul>

      <h3>2. Robots.txt Configuration</h3>
      <ul>
        <li>Dynamically generates robots.txt</li>
        <li>Allows crawlers to access public pages</li>
        <li>Disallows access to private directories</li>
      </ul>
    </div>
  ),

  'guides/i18n': () => (
    <div>
      <h1>Internationalization (i18n) Guide</h1>
      <p>
        This project uses Lingui.js for internationalization, supporting English (en), Simplified
        Chinese (zh-CN), and Traditional Chinese (zh-HK).
      </p>

      <h2>Quick Start</h2>
      <h3>Using Translations in Components</h3>
      <p>
        Use the <code>useLingui</code> hook from <code>@lingui/react</code>:
      </p>
      <pre>
        <code>{`'use client'

import { useLingui } from '@lingui/react'

export function MyComponent() {
  const { i18n } = useLingui()
  
  return (
    <div>
      <h1>{i18n._("hero.title")}</h1>
      <p>{i18n._("hero.description")}</p>
    </div>
  )
}`}</code>
      </pre>
    </div>
  ),

  'guides/radix-ui-theme': () => (
    <div>
      <h1>Radix UI Theme v3 Guide</h1>
      <p>This guide explains how to use Radix UI Theme v3 in the OnlyRules project.</p>

      <h2>Overview</h2>
      <p>
        We've migrated from a mixed Tailwind/custom CSS approach to using Radix UI Theme v3's design
        system. This provides:
      </p>
      <ul>
        <li>Consistent design tokens</li>
        <li>Built-in dark mode support</li>
        <li>Accessible components</li>
        <li>Responsive design patterns</li>
        <li>Better performance</li>
      </ul>

      <h2>Key Changes</h2>
      <h3>1. Component Usage</h3>
      <p>Instead of using HTML elements with Tailwind classes, use Radix UI components:</p>
      <pre>
        <code>{`// ❌ Old approach
<div className="flex flex-col gap-4">
  <h1 className="text-4xl font-bold">Title</h1>
  <p className="text-gray-600">Description</p>
</div>

// ✅ New approach
<Flex direction="column" gap="4">
  <Heading size="8">Title</Heading>
  <Text color="gray">Description</Text>
</Flex>`}</code>
      </pre>
    </div>
  ),
};
