#!/usr/bin/env node

const fs = require('node:fs');
const path = require('node:path');
const { spawn } = require('node:child_process');

// Ensure dist directory exists
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Build CSS using tailwindcss
const tailwindcss = spawn(
  'npx',
  ['tailwindcss', '-i', 'src/styles/index.css', '-o', 'dist/index.css', '--minify'],
  { stdio: 'inherit' }
);

tailwindcss.on('close', (code) => {
  if (code === 0) {
    console.log('✅ CSS build completed successfully');

    // Also create non-minified version for development
    const devBuild = spawn(
      'npx',
      ['tailwindcss', '-i', 'src/styles/index.css', '-o', 'dist/index.css'],
      { stdio: 'inherit' }
    );

    devBuild.on('close', (devCode) => {
      if (devCode === 0) {
        console.log('✅ Development CSS build completed');
      } else {
        console.error('❌ Development CSS build failed');
      }
    });
  } else {
    console.error('❌ CSS build failed');
  }
});
