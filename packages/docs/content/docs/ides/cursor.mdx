---
title: Cursor IDE Integration
description: Learn how to use OnlyRules with Cursor IDE
---

# Cursor IDE Integration

Cursor is an AI-powered code editor that makes it easy to integrate custom AI prompt rules. This guide shows you how to use OnlyRules with Cursor.

## What is Cursor?

Cursor is a fork of VS Code that's designed from the ground up to be the best way to code with AI. It features:
- Built-in AI chat and code generation
- Custom rule support
- Advanced AI features like Composer and Tab completion
- Full VS Code compatibility

## Setting Up Rules in Cursor

### Method 1: Using .cursorrules File

1. **Create a .cursorrules file** in your project root
2. **Copy rule content** from OnlyRules
3. **Paste into .cursorrules** file
4. **Save the file** - Cursor will automatically detect it

Example .cursorrules file:
```
# React Development Rules

You are an expert React developer. When writing React components:

1. Use TypeScript with proper type definitions
2. Follow React best practices and modern patterns
3. Use functional components with hooks
4. Include proper error handling
5. Add JSDoc comments for complex functions
6. Ensure accessibility with ARIA attributes

## Code Style
- Use arrow functions for components
- Prefer const assertions for objects
- Use descriptive variable names
- Keep components small and focused

## Testing
- Write unit tests for all components
- Use React Testing Library
- Test user interactions and edge cases
```

### Method 2: Using Cursor Settings

1. **Open Cursor Settings** (Cmd/Ctrl + ,)
2. **Navigate to AI Rules** section
3. **Add new rule** or import from OnlyRules
4. **Configure rule scope** (global, workspace, or file-specific)

### Method 3: Using OnlyRules Export

1. **Find your rule** on OnlyRules platform
2. **Click Export** button
3. **Select "Cursor (.cursorrules)"** format
4. **Download the file**
5. **Place in your project root**

## Best Practices for Cursor Rules

### Rule Structure

```markdown
# Rule Title

Brief description of what this rule does.

## Context
Explain the role the AI should take.

## Requirements
- List specific requirements
- Include coding standards
- Mention frameworks or libraries

## Examples
Provide concrete examples of desired output.

## Constraints
- What to avoid
- Performance considerations
- Security requirements
```

### Rule Scope

- **Global Rules**: Apply to all projects
- **Workspace Rules**: Apply to current workspace
- **File Rules**: Apply to specific file types

### Performance Tips

1. **Keep rules concise** - Longer rules may impact performance
2. **Use specific examples** - Help the AI understand context quickly
3. **Avoid conflicting rules** - Multiple rules can confuse the AI
4. **Test thoroughly** - Verify rules work as expected

## Popular OnlyRules for Cursor

### React Development
- React Component Generator
- TypeScript React Patterns
- React Testing Best Practices
- React Performance Optimization

### Backend Development
- Node.js API Development
- Database Query Optimization
- Error Handling Patterns
- Security Best Practices

### General Development
- Code Review Guidelines
- Documentation Standards
- Git Commit Messages
- Debugging Techniques

## Troubleshooting

### Rule Not Working
1. Check file placement (.cursorrules in project root)
2. Verify rule syntax
3. Restart Cursor
4. Check for conflicting rules

### Performance Issues
1. Reduce rule complexity
2. Remove unused rules
3. Use more specific contexts
4. Update Cursor to latest version

### AI Not Following Rules
1. Make rules more specific
2. Add concrete examples
3. Use clear, direct language
4. Test with simple cases first

## Advanced Features

### Conditional Rules
Use different rules based on file type or project structure:

```markdown
# Conditional React Rules

If working with React components (.tsx, .jsx):
- Use functional components
- Include PropTypes or TypeScript interfaces

If working with tests (.test.ts, .spec.ts):
- Use describe/it structure
- Include setup and teardown
- Test edge cases
```

### Rule Inheritance
Combine multiple rules for complex projects:

1. Base coding standards
2. Framework-specific rules
3. Project-specific requirements

## Integration with OnlyRules Platform

1. **Browse Rules**: Find community-created rules
2. **Fork and Modify**: Customize existing rules
3. **Share Your Rules**: Contribute back to the community
4. **Stay Updated**: Get notifications for rule updates

## Next Steps

- Explore more [IDE integrations](/docs/ides)
- Learn about [rule creation best practices](/docs/guides)
- Check out [community templates](/templates)
- Join the [OnlyRules community](https://github.com/ranglang/onlyrules)
