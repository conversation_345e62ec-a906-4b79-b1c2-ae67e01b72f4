---
title: API Overview
description: Learn about the OnlyRules API endpoints and how to use them
---

# API Overview

The OnlyRules API provides programmatic access to rules, templates, and user data. All API endpoints are RESTful and return JSON responses.

## Base URL

```
https://onlyrules.app/api
```

## Authentication

Most API endpoints require authentication. Include your API key in the Authorization header:

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
  https://onlyrules.app/api/rules
```

## Rate Limiting

API requests are rate-limited to:
- **Authenticated users**: 1000 requests per hour
- **Unauthenticated users**: 100 requests per hour

Rate limit headers are included in all responses:
- `X-RateLimit-Limit`: Request limit per hour
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Response Format

All API responses follow this format:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 100
  }
}
```

### Error Responses

Error responses include an error object:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "title",
      "issue": "Title is required"
    }
  }
}
```

## Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error

## Available Endpoints

### Rules API
- `GET /api/rules` - List all rules
- `GET /api/rules/{id}` - Get a specific rule
- `POST /api/rules` - Create a new rule
- `PUT /api/rules/{id}` - Update a rule
- `DELETE /api/rules/{id}` - Delete a rule

### Templates API
- `GET /api/templates` - List all templates
- `GET /api/templates/{id}` - Get a specific template

### Users API
- `GET /api/users/me` - Get current user profile
- `PUT /api/users/me` - Update current user profile

### Export API
- `GET /api/rules/export` - Export rules in various formats
- `GET /api/rules/download` - Download rules as files

## SDK and Libraries

We provide official SDKs for popular programming languages:

- **JavaScript/TypeScript**: `npm install @onlyrules/sdk`
- **Python**: `pip install onlyrules`
- **Go**: `go get github.com/onlyrules/go-sdk`

## Examples

### Fetch All Rules

```javascript
const response = await fetch('https://onlyrules.app/api/rules', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  }
});

const data = await response.json();
console.log(data.data); // Array of rules
```

### Create a New Rule

```javascript
const rule = {
  title: "My Custom Rule",
  content: "# My Rule Content",
  ideType: "CURSOR",
  tags: ["javascript", "react"]
};

const response = await fetch('https://onlyrules.app/api/rules', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(rule)
});

const data = await response.json();
console.log(data.data); // Created rule
```

## Webhooks

OnlyRules supports webhooks for real-time notifications:

- `rule.created` - When a new rule is created
- `rule.updated` - When a rule is updated
- `rule.deleted` - When a rule is deleted

Configure webhooks in your account settings.
